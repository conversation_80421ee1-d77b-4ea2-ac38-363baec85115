"""
Gemini AI Service
Integrates with Google's Gemini API to provide NCERT-aligned educational content generation
"""

import google.generativeai as genai
import json
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from pathlib import Path

from config import get_config, MODEL_CONFIGS
from pdf_extractor import NCERTPDFExtractor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiService:
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Gemini service with API key"""
        self.config = get_config()
        self.api_key = api_key or self.config.GEMINI_API_KEY
        
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        
        # Initialize PDF extractor
        self.pdf_extractor = NCERTPDFExtractor(self.config.DATA_DIR)
        
        # Ensure PDF data is extracted
        self._ensure_pdf_data_extracted()
        
        # Load NCERT data
        self.ncert_data = self._load_ncert_data()
        
        # Initialize models
        self.models = {}
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize Gemini models for different tasks"""
        try:
            for task, config in MODEL_CONFIGS.items():
                if config['model_name'].startswith('gemini'):
                    self.models[task] = genai.GenerativeModel(
                        model_name=config['model_name'],
                        generation_config=genai.types.GenerationConfig(
                            max_output_tokens=config.get('max_tokens', 2048),
                            temperature=config.get('temperature', 0.7),
                            top_p=config.get('top_p', 0.9)
                        )
                    )
            logger.info("Gemini models initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing Gemini models: {e}")
            raise
    
    def _ensure_pdf_data_extracted(self):
        """Ensure PDF data is extracted and available"""
        try:
            # Check if Physics 11th PDF data exists
            physics_11_path = Path(self.config.NCERT_DATA_DIR) / "grade_11" / "physics" / "chapters_from_pdf.json"
            
            if not physics_11_path.exists():
                logger.info("PDF data not found, extracting from PDFs...")
                self.pdf_extractor.extract_all_ncert_data()
            else:
                logger.info("PDF data already extracted and available")
                
        except Exception as e:
            logger.error(f"Error ensuring PDF data extraction: {e}")
    
    def _load_ncert_data(self) -> Dict:
        """Load NCERT curriculum data"""
        ncert_data = {}
        try:
            # Load curriculum mapping
            mapping_path = Path(self.config.NCERT_DATA_DIR) / "curriculum_mapping.json"
            if mapping_path.exists():
                with open(mapping_path, 'r', encoding='utf-8') as f:
                    ncert_data['curriculum_mapping'] = json.load(f)
            
            # Load grade-specific data
            ncert_dir = Path(self.config.NCERT_DATA_DIR)
            for grade_dir in ncert_dir.glob("grade_*"):
                if grade_dir.is_dir():
                    grade_num = grade_dir.name.split('_')[1]
                    ncert_data[f'grade_{grade_num}'] = {}
                    
                    for subject_dir in grade_dir.iterdir():
                        if subject_dir.is_dir():
                            subject_data = {}
                            
                            # Load chapters (try PDF-extracted data first)
                            pdf_chapters_file = subject_dir / "chapters_from_pdf.json"
                            chapters_file = subject_dir / "chapters.json"
                            
                            if pdf_chapters_file.exists():
                                with open(pdf_chapters_file, 'r', encoding='utf-8') as f:
                                    subject_data['chapters'] = json.load(f)
                                logger.info(f"Loaded PDF-extracted data for Grade {grade_num} {subject_dir.name}")
                            elif chapters_file.exists():
                                with open(chapters_file, 'r', encoding='utf-8') as f:
                                    subject_data['chapters'] = json.load(f)
                                logger.info(f"Loaded manual data for Grade {grade_num} {subject_dir.name}")
                            
                            ncert_data[f'grade_{grade_num}'][subject_dir.name] = subject_data
            
            logger.info(f"Loaded NCERT data for {len(ncert_data)} components")
            return ncert_data
            
        except Exception as e:
            logger.error(f"Error loading NCERT data: {e}")
            return {}
    
    def get_ncert_context(self, grade: int, subject: str, topic: Optional[str] = None) -> str:
        """Get relevant NCERT context for given parameters"""
        context_parts = []
        
        try:
            # Get curriculum mapping
            if 'curriculum_mapping' in self.ncert_data:
                mapping = self.ncert_data['curriculum_mapping']
                context_parts.append(f"NCERT Curriculum Structure: {json.dumps(mapping, indent=2)}")
            
            # Get grade-specific data
            grade_key = f'grade_{grade}'
            if grade_key in self.ncert_data and subject in self.ncert_data[grade_key]:
                subject_data = self.ncert_data[grade_key][subject]
                
                if 'chapters' in subject_data:
                    chapters_data = subject_data['chapters']
                    context_parts.append(f"Grade {grade} {subject.title()} Content:")
                    
                    # Handle both PDF-extracted and manual data structures
                    if 'chapters' in chapters_data:
                        chapters_list = chapters_data['chapters']
                    else:
                        chapters_list = chapters_data.get('chapters', [])
                    
                    for chapter in chapters_list:
                        # Handle different chapter data structures
                        if 'title' in chapter:
                            chapter_title = chapter['title']
                        elif 'filename' in chapter:
                            chapter_title = chapter['filename'].replace('.pdf', '').replace('_', ' ').title()
                        else:
                            chapter_title = f"Chapter {chapter.get('chapter_number', 'Unknown')}"
                        
                        chapter_info = f"Chapter: {chapter_title}"
                        
                        # Check if this chapter matches the topic
                        topic_match = (topic and 
                                     (topic.lower() in chapter_title.lower() or
                                      any(topic.lower() in section.get('title', '').lower() 
                                          for section in chapter.get('sections', []))))
                        
                        if topic_match or not topic:
                            # Include detailed info for matching topic or all if no specific topic
                            if 'sections' in chapter and chapter['sections']:
                                section_titles = [s.get('title', '') for s in chapter['sections'][:5]]
                                chapter_info += f"\nSections: {', '.join(filter(None, section_titles))}"
                            
                            if 'key_concepts' in chapter and chapter['key_concepts']:
                                chapter_info += f"\nKey Concepts: {', '.join(chapter['key_concepts'][:10])}"
                            elif 'topics' in chapter and chapter['topics']:
                                chapter_info += f"\nTopics: {', '.join(chapter['topics'][:10])}"
                            
                            if 'examples' in chapter and chapter['examples']:
                                chapter_info += f"\nExamples Available: {len(chapter['examples'])}"
                            
                            if 'exercises' in chapter and chapter['exercises']:
                                chapter_info += f"\nExercise Questions: {len(chapter['exercises'])}"
                            
                            # Include some actual content for better context
                            if topic_match and 'full_text' in chapter:
                                # Extract relevant portion of text
                                full_text = chapter['full_text']
                                if len(full_text) > 500:
                                    # Find topic-related content
                                    topic_index = full_text.lower().find(topic.lower())
                                    if topic_index != -1:
                                        start = max(0, topic_index - 100)
                                        end = min(len(full_text), topic_index + 400)
                                        relevant_text = full_text[start:end]
                                        chapter_info += f"\nRelevant Content: ...{relevant_text}..."
                        
                        context_parts.append(chapter_info)
            
            return "\n\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting NCERT context: {e}")
            return f"NCERT context for Grade {grade} {subject}"
    
    def generate_quiz(self, input_data: Dict) -> Dict:
        """Generate quiz using Gemini API with NCERT context"""
        try:
            # Extract parameters
            subject = input_data.get('subject', '')
            topic = input_data.get('topic', '')
            grade = input_data.get('grade', 10)
            question_count = input_data.get('questionCount', 10)
            difficulty = input_data.get('difficulty', 'medium')
            question_types = input_data.get('questionTypes', ['mcq'])
            
            # Get NCERT context
            ncert_context = self.get_ncert_context(grade, subject, topic)
            
            # Create prompt
            prompt = self._create_quiz_prompt(
                subject, topic, grade, question_count, 
                difficulty, question_types, ncert_context
            )
            
            # Generate using Gemini
            model = self.models.get('quiz_generation')
            if not model:
                raise ValueError("Quiz generation model not initialized")
            
            response = model.generate_content(prompt)
            
            # Parse response
            quiz_data = self._parse_quiz_response(
                response.text, subject, topic, grade, difficulty
            )
            
            return {
                "success": True,
                "data": quiz_data,
                "generated_at": datetime.now().isoformat(),
                "model": "gemini-1.5-flash",
                "ncert_aligned": True
            }
            
        except Exception as e:
            logger.error(f"Error generating quiz: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def _create_quiz_prompt(self, subject: str, topic: str, grade: int,
                          question_count: int, difficulty: str, 
                          question_types: List[str], ncert_context: str) -> str:
        """Create detailed prompt for quiz generation"""
        
        prompt = f"""
You are an expert NCERT-aligned educator. Generate a {difficulty} level quiz for Grade {grade} students.

NCERT CONTEXT:
{ncert_context}

QUIZ REQUIREMENTS:
- Subject: {subject}
- Topic: {topic}
- Grade: {grade}
- Number of questions: {question_count}
- Difficulty: {difficulty}
- Question types: {', '.join(question_types)}

IMPORTANT GUIDELINES:
1. All questions MUST be aligned with NCERT curriculum and textbooks
2. Use terminology and concepts exactly as presented in NCERT books
3. Ensure questions test conceptual understanding, not just memorization
4. Include a mix of direct questions and application-based problems
5. Reference specific NCERT chapter content when relevant

For each question, provide:
1. Question text (clear and unambiguous)
2. Question type (mcq, true_false, short_answer, numerical)
3. For MCQ: 4 options with one correct answer
4. For True/False: statement with correct answer
5. For Short Answer: expected answer length and key points
6. For Numerical: step-by-step solution approach
7. Points value (1-5 based on difficulty and complexity)
8. Detailed explanation referencing NCERT content
9. NCERT chapter/section reference if applicable

OUTPUT FORMAT:
Return a valid JSON object with the following structure:
{{
    "title": "Quiz title",
    "subject": "{subject}",
    "topic": "{topic}",
    "grade": {grade},
    "difficulty": "{difficulty}",
    "questions": [
        {{
            "question": "Question text",
            "type": "question_type",
            "options": ["option1", "option2", "option3", "option4"],
            "correct_answer": "correct option or answer",
            "points": 1-5,
            "explanation": "Detailed explanation with NCERT reference",
            "ncert_reference": "Chapter X, Section Y",
            "bloom_level": "remember/understand/apply/analyze"
        }}
    ],
    "total_points": "sum of all question points",
    "estimated_time": "estimated time in minutes",
    "ncert_alignment": true
}}

Generate the quiz now:
"""
        return prompt
    
    def _parse_quiz_response(self, response_text: str, subject: str, 
                           topic: str, grade: int, difficulty: str) -> Dict:
        """Parse Gemini response into structured quiz format"""
        try:
            # Try to parse as JSON first
            if response_text.strip().startswith('{'):
                quiz_data = json.loads(response_text)
                return quiz_data
            
            # If not JSON, extract JSON from response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                quiz_data = json.loads(json_text)
                return quiz_data
            
            # Fallback: create structured quiz from text
            return self._create_fallback_quiz(response_text, subject, topic, grade, difficulty)
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {e}")
            return self._create_fallback_quiz(response_text, subject, topic, grade, difficulty)
    
    def _create_fallback_quiz(self, text: str, subject: str, topic: str, 
                            grade: int, difficulty: str) -> Dict:
        """Create fallback quiz structure when parsing fails"""
        return {
            "title": f"{subject} Quiz: {topic}",
            "subject": subject,
            "topic": topic,
            "grade": grade,
            "difficulty": difficulty,
            "questions": [
                {
                    "question": f"What is the main concept in {topic}?",
                    "type": "mcq",
                    "options": ["Option A", "Option B", "Option C", "Option D"],
                    "correct_answer": "Option A",
                    "points": 1,
                    "explanation": "This tests basic understanding of the concept.",
                    "ncert_reference": f"Grade {grade} {subject} textbook",
                    "bloom_level": "understand"
                }
            ],
            "total_points": 1,
            "estimated_time": 5,
            "ncert_alignment": True
        }
    
    def generate_curriculum(self, input_data: Dict) -> Dict:
        """Generate curriculum using Gemini API with NCERT alignment"""
        try:
            subject = input_data.get('subject', '')
            grade = input_data.get('grade', 10)
            duration = input_data.get('duration', '1 semester')
            
            # Get NCERT context
            ncert_context = self.get_ncert_context(grade, subject)
            
            prompt = f"""
You are an expert NCERT curriculum designer. Create a comprehensive curriculum plan.

NCERT CONTEXT:
{ncert_context}

CURRICULUM REQUIREMENTS:
- Subject: {subject}
- Grade: {grade}
- Duration: {duration}

Create a detailed curriculum that:
1. Follows NCERT guidelines and sequence
2. Covers all mandatory topics for the grade
3. Includes learning objectives aligned with NCERT outcomes
4. Provides assessment strategies
5. Suggests teaching methodologies
6. Includes timeline and pacing

Return a structured JSON response with curriculum details.
"""
            
            model = self.models.get('curriculum_generation')
            response = model.generate_content(prompt)
            
            return {
                "success": True,
                "data": self._parse_curriculum_response(response.text),
                "generated_at": datetime.now().isoformat(),
                "model": "gemini-1.5-pro",
                "ncert_aligned": True
            }
            
        except Exception as e:
            logger.error(f"Error generating curriculum: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def _parse_curriculum_response(self, response_text: str) -> Dict:
        """Parse curriculum response"""
        try:
            # Try to parse as JSON
            if response_text.strip().startswith('{'):
                return json.loads(response_text)
            
            # Extract JSON if embedded
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                return json.loads(json_text)
            
            # Fallback structure
            return {
                "title": "NCERT Aligned Curriculum",
                "description": response_text[:500] + "...",
                "modules": [],
                "assessment_plan": {},
                "resources": []
            }
            
        except Exception as e:
            logger.error(f"Error parsing curriculum response: {e}")
            return {"error": "Failed to parse curriculum response"}
    
    def grade_answer(self, input_data: Dict) -> Dict:
        """Grade student answers using Gemini API"""
        try:
            question = input_data.get('question', '')
            student_answer = input_data.get('student_answer', '')
            correct_answer = input_data.get('correct_answer', '')
            subject = input_data.get('subject', '')
            grade = input_data.get('grade', 10)
            max_points = input_data.get('max_points', 5)
            
            # Get NCERT context for grading criteria
            ncert_context = self.get_ncert_context(grade, subject)
            
            prompt = f"""
You are an expert NCERT-aligned teacher grading student responses.

GRADING CONTEXT:
Subject: {subject}
Grade: {grade}
Max Points: {max_points}

NCERT REFERENCE:
{ncert_context[:1000]}...

QUESTION: {question}

CORRECT ANSWER: {correct_answer}

STUDENT ANSWER: {student_answer}

Grade the student's answer based on:
1. Accuracy of content
2. Use of correct terminology
3. Completeness of response
4. Understanding demonstrated
5. NCERT alignment

Provide:
- Score (0 to {max_points})
- Detailed feedback
- Areas for improvement
- NCERT references for further study

Return as JSON:
{{
    "score": 0-{max_points},
    "feedback": "detailed feedback",
    "strengths": ["strength1", "strength2"],
    "improvements": ["area1", "area2"],
    "ncert_references": ["reference1", "reference2"]
}}
"""
            
            model = self.models.get('grading')
            response = model.generate_content(prompt)
            
            return {
                "success": True,
                "data": self._parse_grading_response(response.text),
                "generated_at": datetime.now().isoformat(),
                "model": "gemini-1.5-flash"
            }
            
        except Exception as e:
            logger.error(f"Error grading answer: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def _parse_grading_response(self, response_text: str) -> Dict:
        """Parse grading response"""
        try:
            if response_text.strip().startswith('{'):
                return json.loads(response_text)
            
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                return json.loads(json_text)
            
            # Fallback
            return {
                "score": 0,
                "feedback": "Unable to process grading response",
                "strengths": [],
                "improvements": ["Please review the answer"],
                "ncert_references": []
            }
            
        except Exception as e:
            logger.error(f"Error parsing grading response: {e}")
            return {
                "score": 0,
                "feedback": "Grading error occurred",
                "strengths": [],
                "improvements": [],
                "ncert_references": []
            }
    
    def generate_content(self, input_data: Dict) -> Dict:
        """Generate educational content using Gemini API"""
        try:
            content_type = input_data.get('type', 'explanation')
            subject = input_data.get('subject', '')
            topic = input_data.get('topic', '')
            grade = input_data.get('grade', 10)
            
            # Get NCERT context
            ncert_context = self.get_ncert_context(grade, subject, topic)
            
            prompt = f"""
Generate {content_type} content for NCERT-aligned education.

NCERT CONTEXT:
{ncert_context}

CONTENT REQUEST:
- Type: {content_type}
- Subject: {subject}
- Topic: {topic}
- Grade: {grade}

Create content that:
1. Aligns with NCERT curriculum
2. Uses appropriate language for grade level
3. Includes examples and illustrations
4. Provides clear explanations
5. References NCERT textbook content

Generate comprehensive {content_type} content now.
"""
            
            model = self.models.get('content_generation')
            response = model.generate_content(prompt)
            
            return {
                "success": True,
                "data": {
                    "content": response.text,
                    "type": content_type,
                    "subject": subject,
                    "topic": topic,
                    "grade": grade,
                    "ncert_aligned": True
                },
                "generated_at": datetime.now().isoformat(),
                "model": "gemini-1.5-pro"
            }
            
        except Exception as e:
            logger.error(f"Error generating content: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }

# Example usage
if __name__ == "__main__":
    service = GeminiService()
    
    # Test quiz generation
    quiz_input = {
        "subject": "mathematics",
        "topic": "Quadratic Equations",
        "grade": 10,
        "questionCount": 5,
        "difficulty": "medium",
        "questionTypes": ["mcq", "short_answer"]
    }
    
    result = service.generate_quiz(quiz_input)
    print("Quiz Generation Result:")
    print(json.dumps(result, indent=2))