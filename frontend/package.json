{"name": "ed<PERSON><PERSON><PERSON>-frontend", "version": "1.0.0", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "axios": "^1.5.0", "react-query": "^3.39.3", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "lucide-react": "^0.279.0", "framer-motion": "^10.16.4", "recharts": "^2.8.0", "react-flow-renderer": "^10.3.17"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test"}, "devDependencies": {"tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5001"}