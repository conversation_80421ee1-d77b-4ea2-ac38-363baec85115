import React from 'react';
import { BookOpen, CheckCircle } from 'lucide-react';

const Physics11Topics = ({ onTopicSelect }) => {
  const availableTopics = [
    {
      title: "Motion in a Straight Line",
      description: "Concepts of position, displacement, velocity, and acceleration",
      chapter: "Chapter 2",
      available: true
    },
    {
      title: "Motion in a Plane",
      description: "Vector addition, projectile motion, and circular motion",
      chapter: "Chapter 3", 
      available: true
    },
    {
      title: "Laws of Motion",
      description: "Newton's laws, friction, and dynamics of motion",
      chapter: "Chapter 4",
      available: true
    },
    {
      title: "Work, Energy and Power",
      description: "Work-energy theorem, conservation of energy, and power",
      chapter: "Chapter 5",
      available: true
    },
    {
      title: "Systems of Particles and Rotational Motion",
      description: "Center of mass, rotational kinematics, and angular momentum",
      chapter: "Chapter 6",
      available: true
    },
    {
      title: "Gravitation",
      description: "Universal law of gravitation, planetary motion, and satellites",
      chapter: "Chapter 7",
      available: true
    },
    {
      title: "Units and Measurement",
      description: "Physical quantities, units, dimensions, and measurement errors",
      chapter: "Chapter 1",
      available: true
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center mb-4">
        <BookOpen className="text-blue-600 mr-3" size={24} />
        <h3 className="text-lg font-semibold text-gray-800">
          Available Physics 11th Topics (NCERT)
        </h3>
      </div>
      
      <div className="space-y-3">
        {availableTopics.map((topic, index) => (
          <div
            key={index}
            className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
              topic.available 
                ? 'border-green-200 bg-green-50 hover:bg-green-100' 
                : 'border-gray-200 bg-gray-50'
            }`}
            onClick={() => topic.available && onTopicSelect && onTopicSelect(topic.title)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center mb-1">
                  <h4 className="font-medium text-gray-900">{topic.title}</h4>
                  {topic.available && (
                    <CheckCircle className="text-green-600 ml-2" size={16} />
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-1">{topic.description}</p>
                <span className="text-xs text-blue-600 font-medium">{topic.chapter}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-800">
          <strong>Note:</strong> These topics are extracted from NCERT Physics Class 11 textbook. 
          Click on any topic to use it in your quiz or curriculum generation.
        </p>
      </div>
    </div>
  );
};

export default Physics11Topics;
