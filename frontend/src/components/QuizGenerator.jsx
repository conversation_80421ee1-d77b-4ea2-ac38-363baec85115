import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { HelpCircle, Loader2 } from 'lucide-react';
import axios from 'axios';
import toast from 'react-hot-toast';
import NCERTContentLoader from './NCERTContentLoader';

const QuizGenerator = ({ onQuizGenerated }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedQuiz, setGeneratedQuiz] = useState(null);
  const [formData, setFormData] = useState(null);
  const { register, handleSubmit, formState: { errors } } = useForm();

  const onSubmit = async (data) => {
    setIsGenerating(true);
    setFormData(data);
    setGeneratedQuiz(null);
    
    try {
      // Try Gemini API first
      let response;
      try {
        response = await axios.post('/api/gemini/quiz/generate', data);
        toast.success('Quiz generated successfully with Gemini AI!');
      } catch (geminiError) {
        console.warn('Gemini API failed, falling back to legacy API:', geminiError);
        response = await axios.post('/api/quiz/generate', data);
        toast.success('Quiz generated successfully!');
      }
      
      const quizData = response.data.data || response.data;
      setGeneratedQuiz(quizData);
      onQuizGenerated(quizData);
    } catch (error) {
      toast.error('Failed to generate quiz');
      console.error('Error:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center mb-6">
        <HelpCircle className="text-green-600 mr-3" size={24} />
        <h2 className="text-xl font-semibold text-gray-800">Generate Quiz</h2>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <p className="text-green-800 text-sm">
          <strong>Note:</strong> Best results with Physics Class 11 topics (Motion, Laws of Motion, Work & Energy, etc.)
          based on NCERT content.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Subject *
            </label>
            <input
              {...register('subject', { required: 'Subject is required' })}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="e.g., Physics (recommended), Mathematics, Science"
              defaultValue="Physics"
            />
            {errors.subject && (
              <p className="text-red-500 text-xs mt-1">{errors.subject.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Topic *
            </label>
            <input
              {...register('topic', { required: 'Topic is required' })}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="e.g., Motion in a Straight Line, Laws of Motion, Work Energy and Power"
              defaultValue="Motion in a Straight Line"
            />
            {errors.topic && (
              <p className="text-red-500 text-xs mt-1">{errors.topic.message}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Grade
            </label>
            <select
              {...register('grade')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">Select Grade</option>
              {[1,2,3,4,5,6,7,8,9,10,11,12].map(grade => (
                <option key={grade} value={grade}>Grade {grade}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Question Count
            </label>
            <input
              {...register('questionCount')}
              type="number"
              min="5"
              max="50"
              defaultValue="10"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Difficulty
            </label>
            <select
              {...register('difficulty')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="easy">Easy</option>
              <option value="medium">Medium</option>
              <option value="hard">Hard</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Question Types
          </label>
          <div className="flex flex-wrap gap-4">
            <label className="flex items-center">
              <input
                {...register('questionTypes')}
                type="checkbox"
                value="mcq"
                defaultChecked
                className="mr-2"
              />
              Multiple Choice
            </label>
            <label className="flex items-center">
              <input
                {...register('questionTypes')}
                type="checkbox"
                value="true_false"
                className="mr-2"
              />
              True/False
            </label>
            <label className="flex items-center">
              <input
                {...register('questionTypes')}
                type="checkbox"
                value="short_answer"
                className="mr-2"
              />
              Short Answer
            </label>
          </div>
        </div>

        <button
          type="submit"
          disabled={isGenerating}
          className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {isGenerating ? (
            <>
              <Loader2 className="animate-spin mr-2" size={20} />
              Generating Quiz...
            </>
          ) : (
            'Generate Quiz'
          )}
        </button>
      </form>
      
      {/* Show NCERT Content Loader when generating or displaying results */}
      {(isGenerating || generatedQuiz) && (
        <div className="mt-6">
          <NCERTContentLoader
            isLoading={isGenerating}
            content={generatedQuiz}
            contentType="quiz"
            grade={formData?.grade}
            subject={formData?.subject}
            topic={formData?.topic}
          />
        </div>
      )}
    </div>
  );
};

export default QuizGenerator;