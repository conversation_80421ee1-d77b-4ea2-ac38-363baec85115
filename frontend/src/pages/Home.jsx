import React from 'react';
import { Link } from 'react-router-dom';
import { BookOpen, HelpCircle, Upload, Presentation, Brain, Calendar, ArrowRight } from 'lucide-react';

const Home = () => {
  const features = [
    {
      icon: BookOpen,
      title: 'Curriculum Generation',
      description: 'AI-powered curriculum creation tailored to your educational needs',
      link: '/curriculum',
      color: 'blue'
    },
    {
      icon: HelpCircle,
      title: 'Quiz Generator',
      description: 'Create engaging quizzes with multiple question types automatically',
      link: '/quiz',
      color: 'green'
    },
    {
      icon: Upload,
      title: 'Answer Assessment',
      description: 'Upload and grade answer sheets using advanced AI technology',
      link: '/assessment',
      color: 'purple'
    },
    {
      icon: Presentation,
      title: 'Slide Generator',
      description: 'Generate beautiful presentation slides for your lessons',
      link: '/slides',
      color: 'orange'
    },
    {
      icon: Brain,
      title: 'Mind Maps',
      description: 'Create visual mind maps to enhance learning and understanding',
      link: '/mindmap',
      color: 'indigo'
    },
    {
      icon: Calendar,
      title: 'Lecture Planner',
      description: 'Plan comprehensive lectures with structured activities',
      link: '/lecture-plan',
      color: 'red'
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: 'text-blue-600 bg-blue-50 hover:bg-blue-100',
      green: 'text-green-600 bg-green-50 hover:bg-green-100',
      purple: 'text-purple-600 bg-purple-50 hover:bg-purple-100',
      orange: 'text-orange-600 bg-orange-50 hover:bg-orange-100',
      indigo: 'text-indigo-600 bg-indigo-50 hover:bg-indigo-100',
      red: 'text-red-600 bg-red-50 hover:bg-red-100'
    };
    return colors[color] || colors.blue;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Welcome to <span className="text-blue-600">EduSarathi</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Your AI-powered educational companion for creating curricula, quizzes, assessments, 
            and learning materials. Empowering educators with intelligent tools for better learning outcomes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/curriculum"
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              Get Started
              <ArrowRight className="ml-2" size={20} />
            </Link>
            <button className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
              Learn More
            </button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Link
                key={index}
                to={feature.link}
                className={`bg-white rounded-xl shadow-md p-6 transition-all duration-300 hover:shadow-lg hover:scale-105 ${getColorClasses(feature.color)}`}
              >
                <div className="flex items-center mb-4">
                  <div className={`p-3 rounded-lg ${getColorClasses(feature.color)}`}>
                    <Icon size={24} />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 mb-4">
                  {feature.description}
                </p>
                <div className="flex items-center text-sm font-medium">
                  Explore
                  <ArrowRight className="ml-1" size={16} />
                </div>
              </Link>
            );
          })}
        </div>

        {/* Stats Section */}
        <div className="mt-20 bg-white rounded-2xl shadow-lg p-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">1000+</div>
              <div className="text-gray-600">Curricula Generated</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600 mb-2">5000+</div>
              <div className="text-gray-600">Quizzes Created</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600 mb-2">2500+</div>
              <div className="text-gray-600">Assessments Graded</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-600 mb-2">15+</div>
              <div className="text-gray-600">Languages Supported</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Transform Your Teaching?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of educators using EduSarathi to create better learning experiences.
          </p>
          <Link
            to="/curriculum"
            className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 inline-flex items-center"
          >
            Start Creating Now
            <ArrowRight className="ml-2" size={20} />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home;