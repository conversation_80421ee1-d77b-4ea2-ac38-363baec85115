import React, { useState } from 'react';
import QuizGenerator from '../components/QuizGenerator';
import Physics11Topics from '../components/Physics11Topics';

const QuizPage = () => {
  const [generatedQuiz, setGeneratedQuiz] = useState(null);

  const handleQuizGenerated = (quiz) => {
    setGeneratedQuiz(quiz);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Quiz Generator</h1>
        <p className="text-gray-600">
          Create engaging quizzes with multiple question types using AI technology.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div>
          <QuizGenerator onQuizGenerated={handleQuizGenerated} />
        </div>

        <div>
          <Physics11Topics onTopicSelect={(topic) => {
            // This could be enhanced to auto-fill the topic in the form
            console.log('Selected topic:', topic);
          }} />
        </div>

        <div>
          {generatedQuiz ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Generated Quiz: {generatedQuiz.title}
              </h2>
              
              <div className="space-y-4">
                {generatedQuiz.description && (
                  <div>
                    <h3 className="font-medium text-gray-700">Description</h3>
                    <p className="text-gray-600">{generatedQuiz.description}</p>
                  </div>
                )}

                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Subject:</span>
                    <p className="text-gray-600">{generatedQuiz.subject}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Topic:</span>
                    <p className="text-gray-600">{generatedQuiz.topic}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Questions:</span>
                    <p className="text-gray-600">{generatedQuiz.questions?.length || 0}</p>
                  </div>
                </div>

                {generatedQuiz.questions && generatedQuiz.questions.length > 0 && (
                  <div>
                    <h3 className="font-medium text-gray-700 mb-3">Questions Preview</h3>
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {generatedQuiz.questions.slice(0, 3).map((question, index) => (
                        <div key={index} className="border border-gray-200 rounded p-4">
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="font-medium text-gray-800">
                              Q{index + 1}. {question.question}
                            </h4>
                            <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {question.type}
                            </span>
                          </div>
                          
                          {question.options && (
                            <div className="space-y-1">
                              {question.options.map((option, optIndex) => (
                                <div key={optIndex} className="text-sm text-gray-600">
                                  {String.fromCharCode(65 + optIndex)}. {option}
                                </div>
                              ))}
                            </div>
                          )}
                          
                          <div className="mt-2 text-xs text-gray-500">
                            Points: {question.points || 1}
                          </div>
                        </div>
                      ))}
                      
                      {generatedQuiz.questions.length > 3 && (
                        <div className="text-center text-gray-500 text-sm">
                          ... and {generatedQuiz.questions.length - 3} more questions
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-6 flex space-x-3">
                <button className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">
                  Start Quiz
                </button>
                <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                  Download PDF
                </button>
                <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-50 transition-colors">
                  Edit Quiz
                </button>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 rounded-lg p-12 text-center">
              <div className="text-gray-400 mb-4">
                <svg className="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Quiz Generated</h3>
              <p className="text-gray-600">
                Fill out the form on the left to generate your quiz.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuizPage;