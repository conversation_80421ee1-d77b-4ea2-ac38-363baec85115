import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';

// Pages
import Home from './pages/Home';
import CurriculumPage from './pages/CurriculumPage';
import QuizPage from './pages/QuizPage';
import AssessmentPage from './pages/AssessmentPage';
import SlidePage from './pages/SlidePage';
import MindMapPage from './pages/MindMapPage';
import LecturePlanPage from './pages/LecturePlanPage';

// Components
import LanguageSelector from './components/LanguageSelector';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <header className="bg-white shadow-sm border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center h-16">
                <div className="flex items-center">
                  <h1 className="text-2xl font-bold text-blue-600">EduSarathi</h1>
                  <span className="ml-2 text-sm text-gray-500">AI Educational Platform</span>
                </div>
                <LanguageSelector />
              </div>
            </div>
          </header>

          <main>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/curriculum" element={<CurriculumPage />} />
              <Route path="/quiz" element={<QuizPage />} />
              <Route path="/assessment" element={<AssessmentPage />} />
              <Route path="/slides" element={<SlidePage />} />
              <Route path="/mindmap" element={<MindMapPage />} />
              <Route path="/lecture-plan" element={<LecturePlanPage />} />
            </Routes>
          </main>

          <Toaster position="top-right" />
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;