# EduSarathi - AI-Powered Educational Platform

EduSarathi is a comprehensive educational platform that leverages AI to generate curricula, create quizzes, grade assessments, generate slides, create mind maps, and plan lectures.

## Features

- **Curriculum Generation**: AI-powered curriculum creation based on topics and learning objectives
- **Quiz Generation**: Automated quiz creation with multiple question types
- **Answer Grading**: AI-based grading system for handwritten and digital answers
- **Slide Generation**: Automatic presentation slide creation
- **Mind Map Creation**: Visual mind maps for better learning comprehension
- **Lecture Planning**: Structured lecture plan generation
- **Multi-language Support**: Translation capabilities using Bhashini API

## Tech Stack

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **Multer** for file uploads

### Frontend
- **React.js** with modern hooks
- **Tailwind CSS** for styling
- **Axios** for API calls

### AI Services
- **Python** with FastAPI
- **OpenAI GPT** for text generation
- **Transformers** for NLP tasks
- **<PERSON><PERSON><PERSON><PERSON>** for AI workflows

## Project Structure

```
EduSarathi/
├── backend/                 # Node.js Backend
├── frontend/               # React Frontend
├── ai/                     # Python AI Services
├── notebooks/              # Jupyter Notebooks
├── models/                 # AI Models
├── data/                   # Datasets
└── docker-compose.yml      # Docker configuration
```

## Quick Start

### Prerequisites
- Node.js (v18+)
- Python (v3.9+)
- MongoDB
- Docker (optional)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd EduSarathi
```

2. **Install dependencies**
```bash
# Install Node.js dependencies
npm run install-all

# Install Python dependencies
pip install -r requirements.txt
```

3. **Environment Setup**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start the services**

**Option 1: Using npm scripts**
```bash
# Start both frontend and backend
npm run dev-all

# Or start individually
npm run backend    # Backend on port 5000
npm run frontend   # Frontend on port 3000
```

**Option 2: Using Docker**
```bash
docker-compose up -d
```

5. **Start AI Service**
```bash
cd ai
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## API Endpoints

### Curriculum
- `POST /api/curriculum/generate` - Generate curriculum
- `GET /api/curriculum/:id` - Get curriculum by ID
- `PUT /api/curriculum/:id` - Update curriculum

### Quiz
- `POST /api/quiz/generate` - Generate quiz
- `GET /api/quiz/:id` - Get quiz by ID
- `POST /api/quiz/:id/submit` - Submit quiz answers

### Grading
- `POST /api/grading/upload` - Upload answer sheet
- `POST /api/grading/grade` - Grade answers
- `GET /api/grading/results/:id` - Get grading results

### Slides
- `POST /api/slides/generate` - Generate slides
- `GET /api/slides/:id` - Get slides by ID

### Mind Maps
- `POST /api/mindmap/generate` - Generate mind map
- `GET /api/mindmap/:id` - Get mind map by ID

### Lecture Plans
- `POST /api/lecture-plan/generate` - Generate lecture plan
- `GET /api/lecture-plan/:id` - Get lecture plan by ID

## Usage Examples

### Generate Curriculum
```javascript
const response = await fetch('/api/curriculum/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    subject: 'Mathematics',
    grade: '10',
    topics: ['Algebra', 'Geometry'],
    duration: '3 months'
  })
});
```

### Generate Quiz
```javascript
const response = await fetch('/api/quiz/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    topic: 'Linear Equations',
    difficulty: 'medium',
    questionCount: 10,
    questionTypes: ['mcq', 'short_answer']
  })
});
```

## Development

### Backend Development
```bash
cd backend
npm run dev
```

### Frontend Development
```bash
cd frontend
npm start
```

### AI Service Development
```bash
cd ai
uvicorn main:app --reload
```

## Testing

```bash
# Backend tests
npm test

# Frontend tests
cd frontend && npm test

# AI service tests
cd ai && python -m pytest
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.

## Support

For support, email <EMAIL> or create an issue in the repository.